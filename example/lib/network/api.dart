import 'dart:convert';
import 'dart:developer' as developer;

import 'package:http/http.dart' as http;

import '../data/system_prompt.dart';
import '../models/resp/chat_completion_resp.dart';
import '../models/resp/image_generation_resp.dart';
import '../services/retry_service.dart';
import '../utils/plot_parser.dart';

class Api {

  static const String _baseUrl = 'https://api.siliconflow.cn/v1';
  static const String _apiKey = 'sk-tgjkyezhlaoopfckhnpymngprlhongchkweqlcipdogvmsfe';

  static final Api _instance = Api._internal();

  factory Api() {
    return _instance;
  }

  Api._internal();

  Future<ChatCompletionResp> chatCompletion(
    List<Map<String, String>> messages, {
    String storyId = 'story_1',
  }) async {
    if (storyId.isEmpty) {
      throw ArgumentError('storyId cannot be empty');
    }

    // 使用重试机制调用API
    final retryResult = await RetryService.executeWithRetry<ChatCompletionResp>(
      operation: (retryPrompt) => _performChatCompletion(messages, storyId, retryPrompt),
      validateResult: (result) => _validateChatCompletionResult(result),
      detectError: (error) => RetryService.detectJsonError('', error),
      maxRetries: 3,
    );

    if (retryResult.success && retryResult.data != null) {
      return retryResult.data!;
    } else {
      throw Exception('聊天完成请求失败: ${retryResult.error}');
    }
  }

  /// 执行单次聊天完成请求
  Future<ChatCompletionResp> _performChatCompletion(
    List<Map<String, String>> messages,
    String storyId,
    String? retryPrompt,
  ) async {
    // 复制消息列表以避免修改原始数据
    final messagesCopy = List<Map<String, String>>.from(messages);

    // 构建系统提示词
    String systemPrompt = SystemPrompt.getSystemPrompt(storyId);
    if (retryPrompt != null) {
      systemPrompt += retryPrompt;
    }

    messagesCopy.insert(0, {
      'role': 'system',
      'content': systemPrompt,
    });

    developer.log(
      '发送聊天完成请求，消息数量: ${messagesCopy.length}',
      name: 'Api.chatCompletion',
    );

    final response = await http.post(
      Uri.parse('$_baseUrl/chat/completions'),
      headers: {
        'Authorization': 'Bearer $_apiKey',
        'Content-Type': 'application/json',
      },
      body: jsonEncode({
        'model': 'Qwen/Qwen2.5-7B-Instruct',
        'messages': messagesCopy,
        'response_format': {
          'type': 'json_object',
        },
      }),
    );

    if (response.statusCode == 200) {
      final responseData = jsonDecode(response.body) as Map<String, dynamic>;
      return ChatCompletionResp.fromJson(responseData);
    } else {
      throw Exception('API请求失败: ${response.statusCode}, ${response.body}');
    }
  }

  /// 验证聊天完成结果
  bool _validateChatCompletionResult(ChatCompletionResp result) {
    try {
      final content = result.choices?.first.message?.content;
      if (content == null || content.isEmpty) {
        return false;
      }

      // 尝试解析内容以验证JSON格式
      final parseResult = PlotParser.parseContent(content);

      // 记录解析结果
      developer.log(
        '内容解析结果: hasPlot=${parseResult.hasPlot}, hasText=${parseResult.hasText}',
        name: 'Api.validation',
      );

      // 如果既没有Plot也没有文本内容，认为是无效的
      return parseResult.hasPlot || parseResult.hasText;
    } catch (e) {
      developer.log(
        '结果验证失败: $e',
        name: 'Api.validation',
        error: e,
      );
      return false;
    }
  }

  Future<ImageGenerationResp> imagesGenerations(String prompt) async {
    final response = await http.post(
      Uri.parse('$_baseUrl/images/generations'),
      headers: {
        'Authorization': 'Bearer $_apiKey',
        'Content-Type': 'application/json',
      },
      body: jsonEncode({
        'model': 'Kwai-Kolors/Kolors',
        'prompt': "美漫风格，Telltale《The Walking Dead》游戏风格，$prompt",
        'image_size': "768x512",
        'batch_size': 1,
        "num_inference_steps": 20,
        "guidance_scale": 7.5
      }),
    );

    if (response.statusCode == 200) {
      return ImageGenerationResp.fromJson(jsonDecode(response.body) as Map<String, dynamic>);
    } else {
      throw Exception('Failed to generate image: ${response.statusCode}, ${response.body}');
    }
  }
}
