import 'dart:developer' as developer;
import 'dart:io';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';

/// 错误日志记录器
class ErrorLogger {
  static const String _logFileName = 'llm_errors.log';
  static const int _maxLogEntries = 1000; // 最大日志条目数
  
  /// 错误统计
  static final Map<String, int> _errorStats = {};
  
  /// 记录JSON解析错误
  static Future<void> logJsonParseError({
    required String errorType,
    required String content,
    required String error,
    int? attemptNumber,
  }) async {
    final timestamp = DateTime.now().toIso8601String();
    final logEntry = {
      'timestamp': timestamp,
      'type': 'json_parse_error',
      'error_type': errorType,
      'content_length': content.length,
      'content_preview': content.length > 200 ? content.substring(0, 200) + '...' : content,
      'error': error,
      'attempt_number': attemptNumber,
    };

    // 更新统计
    _errorStats[errorType] = (_errorStats[errorType] ?? 0) + 1;

    // 记录到开发者日志
    developer.log(
      'JSON解析错误: $errorType (尝试: $attemptNumber)',
      name: 'ErrorLogger',
      error: error,
    );

    // 写入文件日志
    await _writeLogToFile(logEntry);
  }

  /// 记录重试成功事件
  static Future<void> logRetrySuccess({
    required int totalAttempts,
    required List<String> attemptErrors,
  }) async {
    final timestamp = DateTime.now().toIso8601String();
    final logEntry = {
      'timestamp': timestamp,
      'type': 'retry_success',
      'total_attempts': totalAttempts,
      'attempt_errors': attemptErrors,
    };

    developer.log(
      '重试成功，总尝试次数: $totalAttempts',
      name: 'ErrorLogger',
    );

    await _writeLogToFile(logEntry);
  }

  /// 记录重试失败事件
  static Future<void> logRetryFailure({
    required int totalAttempts,
    required List<String> attemptErrors,
    required String finalError,
  }) async {
    final timestamp = DateTime.now().toIso8601String();
    final logEntry = {
      'timestamp': timestamp,
      'type': 'retry_failure',
      'total_attempts': totalAttempts,
      'attempt_errors': attemptErrors,
      'final_error': finalError,
    };

    developer.log(
      '重试失败，总尝试次数: $totalAttempts',
      name: 'ErrorLogger',
      error: finalError,
    );

    await _writeLogToFile(logEntry);
  }

  /// 获取错误统计
  static Map<String, int> getErrorStats() {
    return Map.from(_errorStats);
  }

  /// 清除错误统计
  static void clearErrorStats() {
    _errorStats.clear();
  }

  /// 写入日志到文件
  static Future<void> _writeLogToFile(Map<String, dynamic> logEntry) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logFile = File('${directory.path}/$_logFileName');

      // 读取现有日志
      List<Map<String, dynamic>> logs = [];
      if (await logFile.exists()) {
        final content = await logFile.readAsString();
        final lines = content.split('\n').where((line) => line.trim().isNotEmpty);
        for (final line in lines) {
          try {
            logs.add(jsonDecode(line));
          } catch (e) {
            // 忽略无法解析的行
          }
        }
      }

      // 添加新日志条目
      logs.add(logEntry);

      // 保持日志条目数量在限制内
      if (logs.length > _maxLogEntries) {
        logs = logs.sublist(logs.length - _maxLogEntries);
      }

      // 写入文件
      final logLines = logs.map((log) => jsonEncode(log)).join('\n');
      await logFile.writeAsString(logLines + '\n');
    } catch (e) {
      developer.log(
        '写入日志文件失败: $e',
        name: 'ErrorLogger',
        error: e,
      );
    }
  }

  /// 读取日志文件
  static Future<List<Map<String, dynamic>>> readLogs() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logFile = File('${directory.path}/$_logFileName');

      if (!await logFile.exists()) {
        return [];
      }

      final content = await logFile.readAsString();
      final lines = content.split('\n').where((line) => line.trim().isNotEmpty);
      
      final logs = <Map<String, dynamic>>[];
      for (final line in lines) {
        try {
          logs.add(jsonDecode(line));
        } catch (e) {
          // 忽略无法解析的行
        }
      }

      return logs;
    } catch (e) {
      developer.log(
        '读取日志文件失败: $e',
        name: 'ErrorLogger',
        error: e,
      );
      return [];
    }
  }

  /// 清除日志文件
  static Future<void> clearLogs() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logFile = File('${directory.path}/$_logFileName');

      if (await logFile.exists()) {
        await logFile.delete();
      }

      clearErrorStats();
    } catch (e) {
      developer.log(
        '清除日志文件失败: $e',
        name: 'ErrorLogger',
        error: e,
      );
    }
  }

  /// 获取日志文件大小
  static Future<int> getLogFileSize() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logFile = File('${directory.path}/$_logFileName');

      if (await logFile.exists()) {
        return await logFile.length();
      }
      return 0;
    } catch (e) {
      return 0;
    }
  }

  /// 生成错误报告
  static Future<String> generateErrorReport() async {
    final logs = await readLogs();
    final stats = getErrorStats();
    
    final report = StringBuffer();
    report.writeln('=== LLM错误报告 ===');
    report.writeln('生成时间: ${DateTime.now().toIso8601String()}');
    report.writeln('总日志条目: ${logs.length}');
    report.writeln();
    
    report.writeln('=== 错误统计 ===');
    if (stats.isEmpty) {
      report.writeln('暂无错误统计数据');
    } else {
      stats.forEach((errorType, count) {
        report.writeln('$errorType: $count 次');
      });
    }
    report.writeln();
    
    // 最近的错误
    final recentErrors = logs.where((log) => log['type'] == 'json_parse_error').take(10);
    if (recentErrors.isNotEmpty) {
      report.writeln('=== 最近的JSON解析错误 ===');
      for (final error in recentErrors) {
        report.writeln('时间: ${error['timestamp']}');
        report.writeln('类型: ${error['error_type']}');
        report.writeln('内容长度: ${error['content_length']}');
        report.writeln('错误: ${error['error']}');
        report.writeln('---');
      }
    }
    
    return report.toString();
  }
}
