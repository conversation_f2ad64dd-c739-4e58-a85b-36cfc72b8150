import 'dart:convert';
import 'dart:developer' as developer;
import '../models/plot.dart';
import '../models/message.dart';
import '../network/api.dart';
import '../services/retry_service.dart';

/// Plot解析器，用于解析AI返回的文本内容
class PlotParser {
  /// 解析AI返回的文本内容，尝试提取Plot结构
  static PlotParseResult parseContent(String content) {
    if (content.trim().isEmpty) {
      return PlotParseResult.textOnly(content);
    }

    // 检查是否是无效的JSON片段（如单独的"["或"]"）
    final trimmedContent = content.trim();
    if (_isInvalidJsonFragment(trimmedContent)) {
      developer.log(
        '检测到无效JSON片段: $trimmedContent',
        name: 'PlotParser.parseContent',
      );
      return PlotParseResult.textOnly('检测到无效的JSON片段，请重试');
    }

    // 尝试解析JSON格式
    final jsonResult = _tryParseJson(content);
    if (jsonResult != null) {
      return jsonResult;
    }

    // 尝试解析混合格式（JSON + 文本）
    final mixedResult = _tryParseMixed(content);
    if (mixedResult != null) {
      return mixedResult;
    }

    // 如果都无法解析，返回纯文本
    return PlotParseResult.textOnly(content);
  }

  /// 检查是否是无效的JSON片段
  static bool _isInvalidJsonFragment(String content) {
    // 检查是否只包含JSON的开始或结束符号
    final invalidPatterns = [
      RegExp(r'^\s*\[\s*$'),           // 只有 "["
      RegExp(r'^\s*\]\s*$'),           // 只有 "]"
      RegExp(r'^\s*\{\s*$'),           // 只有 "{"
      RegExp(r'^\s*\}\s*$'),           // 只有 "}"
      RegExp(r'^\s*\[\s*,\s*$'),       // "[,"
      RegExp(r'^\s*,\s*\]\s*$'),       // ",]"
      RegExp(r'^\s*\[\s*,\s*\]\s*$'),  // "[,]"
      RegExp(r'^\s*\[\s*&\s*\]\s*$'),  // "[&]"
      RegExp(r'^\s*\[\s*"\s*,\s*&\s*"\s*\]\s*$'), // ["",&"]
    ];

    for (final pattern in invalidPatterns) {
      if (pattern.hasMatch(content)) {
        return true;
      }
    }

    // 检查是否只包含无意义的字符组合
    if (content.length < 10 &&
        content.contains(RegExp(r'^[\[\]{},"&\s]*$'))) {
      return true;
    }

    return false;
  }

  /// 尝试解析纯JSON格式
  static PlotParseResult? _tryParseJson(String content) {
    try {
      final cleanContent = content.replaceAll("```", "").trim();

      // 记录尝试解析的内容
      developer.log(
        '尝试解析JSON，内容长度: ${cleanContent.length}',
        name: 'PlotParser.parseJson',
      );

      final jsonData = jsonDecode(cleanContent);
      if (jsonData is Map<String, dynamic>) {
        final plot = Plot.fromJson(jsonData);

        developer.log(
          'JSON解析成功',
          name: 'PlotParser.parseJson',
        );

        return PlotParseResult.fromPlot(plot);
      } else if (jsonData is List) {
        // 处理数组格式的响应（如您提供的错误示例）
        return _tryParseJsonArray(jsonData, cleanContent);
      }
    } catch (e) {
      // 记录JSON解析错误
      final errorType = RetryService.detectJsonError(content, e);
      RetryService.logJsonParseError(errorType, content, e);

      developer.log(
        'JSON解析失败: $e',
        name: 'PlotParser.parseJson',
        error: e,
      );
    }
    return null;
  }

  /// 尝试解析JSON数组格式
  static PlotParseResult? _tryParseJsonArray(List<dynamic> jsonArray, String originalContent) {
    try {
      developer.log(
        '检测到JSON数组格式，数组长度: ${jsonArray.length}',
        name: 'PlotParser.parseArray',
      );

      // 尝试将数组中的对象合并为单个Plot对象
      final mergedData = <String, dynamic>{};

      for (final item in jsonArray) {
        if (item is Map<String, dynamic>) {
          mergedData.addAll(item);
        }
      }

      if (mergedData.isNotEmpty) {
        final plot = Plot.fromJson(mergedData);

        developer.log(
          '数组格式解析成功',
          name: 'PlotParser.parseArray',
        );

        return PlotParseResult.fromPlot(plot);
      }
    } catch (e) {
      developer.log(
        '数组格式解析失败: $e',
        name: 'PlotParser.parseArray',
        error: e,
      );
    }
    return null;
  }

  /// 尝试解析混合格式（可能包含JSON块和普通文本）
  static PlotParseResult? _tryParseMixed(String content) {
    developer.log(
      '尝试解析混合格式',
      name: 'PlotParser.parseMixed',
    );

    // 查找JSON块的模式，支持更复杂的嵌套结构
    final jsonPattern = RegExp(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}');
    final matches = jsonPattern.allMatches(content);

    developer.log(
      '找到${matches.length}个可能的JSON块',
      name: 'PlotParser.parseMixed',
    );

    for (final match in matches) {
      try {
        final jsonStr = match.group(0);
        if (jsonStr != null) {
          developer.log(
            '尝试解析JSON块: ${jsonStr.length > 100 ? jsonStr.substring(0, 100) + '...' : jsonStr}',
            name: 'PlotParser.parseMixed',
          );

          final jsonData = jsonDecode(jsonStr);
          if (jsonData is Map<String, dynamic>) {
            final plot = Plot.fromJson(jsonData);

            // 提取JSON之外的文本作为额外内容
            final beforeJson = content.substring(0, match.start).trim();
            final afterJson = content.substring(match.end).trim();
            final extraText = [beforeJson, afterJson]
                .where((text) => text.isNotEmpty)
                .join('\n')
                .trim();

            developer.log(
              '混合格式解析成功，额外文本长度: ${extraText.length}',
              name: 'PlotParser.parseMixed',
            );

            return PlotParseResult.fromPlot(plot, extraText: extraText);
          }
        }
      } catch (e) {
        developer.log(
          'JSON块解析失败: $e',
          name: 'PlotParser.parseMixed',
          error: e,
        );
        // 继续尝试下一个匹配
        continue;
      }
    }

    developer.log(
      '混合格式解析失败',
      name: 'PlotParser.parseMixed',
    );

    return null;
  }

  /// 从Plot对象生成消息列表
  static PlotMessageResult generateMessagesFromPlot(PlotParseResult parseResult, String baseId) {
    final messages = <Message>[];
    final timestamp = DateTime.now();
    int messageIndex = 0;
    String? imagePrompt;

    // 添加额外文本消息（如果有）
    if (parseResult.extraText != null && parseResult.extraText!.isNotEmpty) {
      messages.add(Message.textMessage(
        id: '${baseId}_extra_${messageIndex++}',
        content: parseResult.extraText!,
        sender: MessageSender.assistant,
        timestamp: timestamp.add(Duration(milliseconds: messageIndex * 100)),
      ));
    }

    final plot = parseResult.plot;
    if (plot == null) {
      // 如果没有Plot，添加纯文本消息
      if (parseResult.textContent.isNotEmpty) {
        messages.add(Message.textMessage(
          id: '${baseId}_text_${messageIndex++}',
          content: parseResult.textContent,
          sender: MessageSender.assistant,
          timestamp: timestamp.add(Duration(milliseconds: messageIndex * 100)),
        ));
      }
      return PlotMessageResult(messages: messages);
    }

    // 检查是否有图像提示词
    if (plot.imagePrompt != null && plot.imagePrompt!.isNotEmpty) {
      imagePrompt = plot.imagePrompt!;

      // 添加图像占位符消息
      final imageMessage = Message.imageMessage(
        id: '${baseId}_image_${messageIndex++}',
        imageUrl: '', // 占位符，稍后更新
        content: '正在生成场景图像...',
        timestamp: timestamp.add(Duration(milliseconds: messageIndex * 100)),
      );
      messages.add(imageMessage);
    }

    // 添加剧情描述消息
    if (plot.plotDescription?.content != null && plot.plotDescription!.content!.isNotEmpty) {
      messages.add(Message.plotMessage(
        id: '${baseId}_plot_${messageIndex++}',
        content: plot.plotDescription!.content!,
        timestamp: timestamp.add(Duration(milliseconds: messageIndex * 100)),
      ));
    }

    // 添加对话消息
    if (plot.dialogue?.content != null && plot.dialogue!.content!.isNotEmpty) {
      String dialogueContent = plot.dialogue!.content!;
      
      // 如果有角色信息，添加到内容前面
      if (plot.dialogue!.character != null && plot.dialogue!.character!.isNotEmpty) {
        String characterInfo = plot.dialogue!.character!;
        if (plot.dialogue!.status != null && plot.dialogue!.status!.isNotEmpty) {
          characterInfo += '（${plot.dialogue!.status!}）';
        }
        dialogueContent = '$characterInfo：$dialogueContent';
      }

      messages.add(Message.textMessage(
        id: '${baseId}_dialogue_${messageIndex++}',
        content: dialogueContent,
        sender: MessageSender.assistant,
        timestamp: timestamp.add(Duration(milliseconds: messageIndex * 100)),
      ));
    }

    // 添加选择消息
    if (plot.choices?.options != null && plot.choices!.options!.isNotEmpty) {
      final choices = plot.choices!.options!.map((option) {
        return MessageChoice(
          id: (option.id ?? 0).toInt(),
          text: option.text ?? '',
        );
      }).toList();

      messages.add(Message.choicesMessage(
        id: '${baseId}_choices_${messageIndex++}',
        content: '请选择你的行动：',
        choices: choices,
        timestamp: timestamp.add(Duration(milliseconds: messageIndex * 100)),
      ));
    }

    return PlotMessageResult(
      messages: messages,
      imagePrompt: imagePrompt,
      imageMessageId: imagePrompt != null ? '${baseId}_image_0' : null,
    );
  }

  /// 生成图像并返回图像URL
  static Future<String?> generateImage(String imagePrompt) async {
    try {
      final response = await Api().imagesGenerations(imagePrompt);
      final imageUrl = response.images?.first.url ?? response.data?.first.url;
      return imageUrl;
    } catch (e) {
      print('图像生成失败: $e');
      return null;
    }
  }
}

/// Plot解析结果
class PlotParseResult {
  final Plot? plot;
  final String textContent;
  final String? extraText;

  PlotParseResult({
    this.plot,
    required this.textContent,
    this.extraText,
  });

  factory PlotParseResult.fromPlot(Plot plot, {String? extraText}) {
    return PlotParseResult(
      plot: plot,
      textContent: '',
      extraText: extraText,
    );
  }

  factory PlotParseResult.textOnly(String text) {
    return PlotParseResult(
      plot: null,
      textContent: text,
    );
  }

  bool get hasPlot => plot != null;
  bool get hasText => textContent.isNotEmpty;
  bool get hasExtraText => extraText != null && extraText!.isNotEmpty;
}

/// Plot消息生成结果
class PlotMessageResult {
  final List<Message> messages;
  final String? imagePrompt;
  final String? imageMessageId;

  PlotMessageResult({
    required this.messages,
    this.imagePrompt,
    this.imageMessageId,
  });

  bool get hasImagePrompt => imagePrompt != null && imagePrompt!.isNotEmpty;
}
