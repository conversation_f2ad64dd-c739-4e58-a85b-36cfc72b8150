import 'dart:convert';
import 'dart:developer' as developer;
import '../utils/error_logger.dart';

/// JSON解析错误类型
enum JsonParseErrorType {
  invalidJson,
  missingFields,
  typeError,
  invalidFragment,  // 新增：无效JSON片段
  unknown,
}

/// 重试结果
class RetryResult<T> {
  final T? data;
  final bool success;
  final String? error;
  final int attemptCount;
  final List<String> attemptErrors;

  RetryResult({
    this.data,
    required this.success,
    this.error,
    required this.attemptCount,
    required this.attemptErrors,
  });

  factory RetryResult.success(T data, int attemptCount, List<String> attemptErrors) {
    return RetryResult(
      data: data,
      success: true,
      attemptCount: attemptCount,
      attemptErrors: attemptErrors,
    );
  }

  factory RetryResult.failure(String error, int attemptCount, List<String> attemptErrors) {
    return RetryResult(
      success: false,
      error: error,
      attemptCount: attemptCount,
      attemptErrors: attemptErrors,
    );
  }
}

/// LLM重试服务
class RetryService {
  static const int _defaultMaxRetries = 3;
  static const Duration _defaultRetryDelay = Duration(milliseconds: 500);

  /// 检测JSON解析错误类型
  static JsonParseErrorType detectJsonError(String content, dynamic error) {
    final trimmedContent = content.replaceAll("```", "").trim();

    // 首先检查是否是无效JSON片段
    if (_isInvalidJsonFragment(trimmedContent)) {
      return JsonParseErrorType.invalidFragment;
    }

    try {
      jsonDecode(trimmedContent);
      // 如果JSON解析成功，但Plot.fromJson失败，说明是字段问题
      return JsonParseErrorType.missingFields;
    } catch (e) {
      final errorStr = e.toString().toLowerCase();
      if (errorStr.contains('unexpected character') ||
          errorStr.contains('unexpected end') ||
          errorStr.contains('invalid json')) {
        return JsonParseErrorType.invalidJson;
      } else if (errorStr.contains('type') || errorStr.contains('cast')) {
        return JsonParseErrorType.typeError;
      }
      return JsonParseErrorType.unknown;
    }
  }

  /// 检查是否是无效的JSON片段
  static bool _isInvalidJsonFragment(String content) {
    // 检查是否只包含JSON的开始或结束符号
    final invalidPatterns = [
      RegExp(r'^\s*\[\s*$'),           // 只有 "["
      RegExp(r'^\s*\]\s*$'),           // 只有 "]"
      RegExp(r'^\s*\{\s*$'),           // 只有 "{"
      RegExp(r'^\s*\}\s*$'),           // 只有 "}"
      RegExp(r'^\s*\[\s*,\s*$'),       // "[,"
      RegExp(r'^\s*,\s*\]\s*$'),       // ",]"
      RegExp(r'^\s*\[\s*,\s*\]\s*$'),  // "[,]"
      RegExp(r'^\s*\[\s*&\s*\]\s*$'),  // "[&]"
      RegExp(r'^\s*\[\s*"\s*,\s*&\s*"\s*\]\s*$'), // ["",&"]
    ];

    for (final pattern in invalidPatterns) {
      if (pattern.hasMatch(content)) {
        return true;
      }
    }

    // 检查是否只包含无意义的字符组合
    if (content.length < 10 &&
        content.contains(RegExp(r'^[\[\]{},"&\s]*$'))) {
      return true;
    }

    return false;
  }

  /// 生成重试时的额外提示
  static String generateRetryPrompt(JsonParseErrorType errorType, int attemptNumber) {
    final basePrompt = "\n\n【重要提醒 - 第${attemptNumber}次尝试】\n";

    switch (errorType) {
      case JsonParseErrorType.invalidFragment:
        return basePrompt +
            "上次输出了无效的JSON片段（如单独的\"[\"或\"]\"），这是完全错误的！\n" +
            "请务必输出完整的JSON对象，格式如下：\n" +
            "{\n" +
            "  \"plot_description\": {\"content\": \"剧情描述\"},\n" +
            "  \"dialogue\": {\"character\": \"角色\", \"status\": \"状态\", \"content\": \"对话\"},\n" +
            "  \"choices\": {\"options\": [{\"id\": 1, \"text\": \"选择1\"}, {\"id\": 2, \"text\": \"选择2\"}, {\"id\": 3, \"text\": \"(自由对话)\"}]},\n" +
            "  \"image_prompt\": \"场景描述\"\n" +
            "}\n" +
            "绝对不要输出单独的符号或不完整的JSON！\n\n";

      case JsonParseErrorType.invalidJson:
        return basePrompt +
            "上次输出的JSON格式不正确，请确保：\n" +
            "1. 使用正确的JSON语法，所有字符串都用双引号包围\n" +
            "2. 对象和数组的括号正确匹配\n" +
            "3. 不要在JSON中添加任何注释或额外文本\n" +
            "4. 确保所有字段名都用双引号包围\n\n";

      case JsonParseErrorType.missingFields:
        return basePrompt +
            "上次输出缺少必要字段，请确保包含以下完整结构：\n" +
            "- plot_description: {\"content\": \"剧情描述\"}\n" +
            "- dialogue: {\"character\": \"角色名\", \"status\": \"状态\", \"content\": \"对话内容\"}\n" +
            "- choices: {\"options\": [{\"id\": 1, \"text\": \"选择文本\"}, ...]}\n" +
            "- image_prompt: \"场景描述\" (可选)\n\n";

      case JsonParseErrorType.typeError:
        return basePrompt +
            "上次输出的数据类型不正确，请确保：\n" +
            "1. id字段使用数字类型，不要用字符串\n" +
            "2. 所有文本内容使用字符串类型\n" +
            "3. options是数组类型\n" +
            "4. 严格按照示例格式输出\n\n";

      case JsonParseErrorType.unknown:
        return basePrompt +
            "上次输出格式有误，请严格按照以下JSON格式输出，不要添加任何其他内容：\n" +
            "{\n" +
            "  \"plot_description\": {\"content\": \"剧情描述\"},\n" +
            "  \"dialogue\": {\"character\": \"角色\", \"status\": \"状态\", \"content\": \"对话\"},\n" +
            "  \"choices\": {\"options\": [{\"id\": 1, \"text\": \"选择1\"}, {\"id\": 2, \"text\": \"选择2\"}, {\"id\": 3, \"text\": \"(自由对话)\"}]},\n" +
            "  \"image_prompt\": \"场景描述\"\n" +
            "}\n\n";
    }
  }

  /// 执行带重试的操作
  static Future<RetryResult<T>> executeWithRetry<T>({
    required Future<T> Function(String? retryPrompt) operation,
    required bool Function(T result) validateResult,
    required JsonParseErrorType Function(dynamic error) detectError,
    int maxRetries = _defaultMaxRetries,
    Duration retryDelay = _defaultRetryDelay,
  }) async {
    final attemptErrors = <String>[];
    String? retryPrompt;

    for (int attempt = 1; attempt <= maxRetries + 1; attempt++) {
      try {
        developer.log(
          'LLM API调用尝试 $attempt/${maxRetries + 1}',
          name: 'RetryService',
        );

        final result = await operation(retryPrompt);
        
        if (validateResult(result)) {
          developer.log(
            'LLM API调用成功，尝试次数: $attempt',
            name: 'RetryService',
          );

          // 记录重试成功事件
          if (attempt > 1) {
            ErrorLogger.logRetrySuccess(
              totalAttempts: attempt,
              attemptErrors: attemptErrors,
            );
          }

          return RetryResult.success(result, attempt, attemptErrors);
        } else {
          final error = '结果验证失败';
          attemptErrors.add('尝试$attempt: $error');
          
          if (attempt <= maxRetries) {
            final errorType = detectError(error);
            retryPrompt = generateRetryPrompt(errorType, attempt + 1);
            await Future.delayed(retryDelay);
          }
        }
      } catch (e) {
        final errorMsg = e.toString();
        attemptErrors.add('尝试$attempt: $errorMsg');
        
        developer.log(
          'LLM API调用失败，尝试 $attempt/${maxRetries + 1}: $errorMsg',
          name: 'RetryService',
          error: e,
        );

        if (attempt <= maxRetries) {
          final errorType = detectError(e);
          retryPrompt = generateRetryPrompt(errorType, attempt + 1);
          await Future.delayed(retryDelay);
        }
      }
    }

    final finalError = '重试${maxRetries + 1}次后仍然失败';
    developer.log(
      finalError,
      name: 'RetryService',
      error: attemptErrors.join('; '),
    );

    // 记录重试失败事件
    ErrorLogger.logRetryFailure(
      totalAttempts: maxRetries + 1,
      attemptErrors: attemptErrors,
      finalError: finalError,
    );

    return RetryResult.failure(finalError, maxRetries + 1, attemptErrors);
  }

  /// 记录JSON解析错误统计
  static void logJsonParseError(JsonParseErrorType errorType, String content, [dynamic error, int? attemptNumber]) {
    developer.log(
      'JSON解析错误: ${errorType.name}',
      name: 'RetryService.JsonError',
      error: '内容长度: ${content.length}, 前100字符: ${content.length > 100 ? content.substring(0, 100) : content}',
    );

    // 异步记录到文件
    ErrorLogger.logJsonParseError(
      errorType: errorType.name,
      content: content,
      error: error?.toString() ?? 'Unknown error',
      attemptNumber: attemptNumber,
    );
  }
}
